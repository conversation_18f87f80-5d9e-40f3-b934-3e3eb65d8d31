@echo off
echo === Symfony Dumper 连接测试 ===
echo.

set /p port="请输入端口号 (默认9912): "
if "%port%"=="" set port=9912

echo 测试端口: %port%
echo.

echo 1. 检查端口是否开放...
netstat -an | findstr ":%port% " >nul
if %errorlevel%==0 (
    echo ✓ 端口 %port% 正在监听
) else (
    echo ✗ 端口 %port% 没有在监听
    echo 请确保IDEA插件中的Dumper服务器已启动
    goto :end
)

echo.
echo 2. 测试HTTP连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n连接时间: %%{time_connect}s\n" http://localhost:%port%/
if %errorlevel%==0 (
    echo ✓ HTTP连接成功
) else (
    echo ✗ HTTP连接失败
    echo 请检查服务器是否正常运行
)

echo.
echo 3. 发送测试数据...
echo {"test": true, "message": "Connection test", "timestamp": "%date% %time%"} > temp_test.json
curl -X POST -H "Content-Type: application/json" -d @temp_test.json http://localhost:%port%/
del temp_test.json
echo.

echo.
echo 测试完成！
echo 如果所有测试都通过，请检查IDEA中的Symfony Dumper工具窗口是否显示了数据。

:end
pause
