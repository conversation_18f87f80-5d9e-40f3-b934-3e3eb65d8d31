# Symfony Dumper IDEA Plugin - 安装指南

## 前置要求

### 1. Java 开发环境
插件需要Java 21或更高版本来构建。

#### 下载和安装Java 21
1. 访问 [Oracle JDK](https://www.oracle.com/java/technologies/downloads/) 或 [OpenJDK](https://adoptium.net/)
2. 下载Java 21 JDK
3. 安装到系统中（例如：`C:\Program Files\Java\jdk-21`）

#### 设置环境变量
1. 右键点击"此电脑" -> "属性" -> "高级系统设置" -> "环境变量"
2. 在"系统变量"中点击"新建"：
   - 变量名：`JAVA_HOME`
   - 变量值：Java安装路径（例如：`C:\Program Files\Java\jdk-21`）
3. 编辑"Path"变量，添加：`%JAVA_HOME%\bin`
4. 点击"确定"保存

#### 验证Java安装
打开命令提示符，运行：
```cmd
java -version
javac -version
```

应该显示Java 21的版本信息。

### 2. IntelliJ IDEA
确保安装了IntelliJ IDEA 2025.1或更高版本。

## 构建插件

### 方法1：使用提供的批处理文件
```cmd
build.bat
```

### 方法2：手动构建
```cmd
gradlew.bat buildPlugin
```

### 方法3：在IDEA中构建
1. 用IntelliJ IDEA打开项目
2. 打开Gradle工具窗口
3. 展开 `Tasks` -> `intellij`
4. 双击 `buildPlugin`

## 安装插件

### 1. 从磁盘安装
1. 打开IntelliJ IDEA
2. 进入 `File` -> `Settings` -> `Plugins`
3. 点击齿轮图标，选择 `Install Plugin from Disk...`
4. 选择构建生成的文件：`build/distributions/thinkphp-1.0-SNAPSHOT.zip`
5. 点击"OK"
6. 重启IDEA

### 2. 验证安装
重启IDEA后：
1. 在底部工具栏应该能看到"Symfony Dumper"标签
2. 点击标签打开工具窗口
3. 界面应该显示端口设置和启动按钮

## 使用插件

### 1. 启动Dumper服务器
1. 打开"Symfony Dumper"工具窗口
2. 设置端口号（默认9912）
3. 点击"Start Server"
4. 状态应该显示"Server running on port 9912"

### 2. 测试连接
运行提供的测试脚本：
```cmd
php test_dumper.php
```

### 3. 配置Symfony应用
在Symfony项目中配置dump服务器：

```yaml
# config/packages/debug.yaml
when@dev:
    debug:
        dump_destination: "tcp://127.0.0.1:9912"
```

## 故障排除

### 构建问题
1. **JAVA_HOME未设置**：按照上面的步骤设置Java环境变量
2. **Java版本过低**：确保使用Java 21或更高版本
3. **网络问题**：确保能够访问Maven仓库

### 运行问题
1. **端口被占用**：更改端口号或关闭占用端口的程序
2. **防火墙阻止**：允许Java程序通过防火墙
3. **权限问题**：以管理员身份运行IDEA

### 插件不显示
1. 确认插件已正确安装
2. 检查IDEA版本兼容性
3. 查看IDEA日志文件中的错误信息

## 开发模式

如果你想在开发模式下运行插件：

```cmd
gradlew.bat runIde
```

这将启动一个带有插件的IDEA实例用于测试。

## 支持

如果遇到问题，请检查：
1. IDEA的事件日志（Help -> Show Log in Explorer）
2. 插件是否与当前IDEA版本兼容
3. Java环境是否正确配置
