# Symfony Dumper IDEA Plugin (Web UI) - 安装指南

## 前置要求

### 1. IntelliJ IDEA
- IntelliJ IDEA 2020.1或更高版本
- 必须支持JCEF (Java Chromium Embedded Framework)
- 推荐使用最新版本的IDEA

### 2. Node.js 开发环境
插件的Web UI需要Node.js来构建和运行。

#### 下载和安装Node.js
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本（推荐16.x或更高版本）
3. 安装到系统中

#### 验证Node.js安装
打开命令提示符，运行：
```cmd
node --version
npm --version
```

应该显示Node.js和npm的版本信息。

### 3. Java 开发环境
插件构建需要Java 21或更高版本。

#### 下载和安装Java 21
1. 访问 [Oracle JDK](https://www.oracle.com/java/technologies/downloads/) 或 [OpenJDK](https://adoptium.net/)
2. 下载Java 21 JDK
3. 安装到系统中（例如：`C:\Program Files\Java\jdk-21`）

#### 设置环境变量
1. 右键点击"此电脑" -> "属性" -> "高级系统设置" -> "环境变量"
2. 在"系统变量"中点击"新建"：
   - 变量名：`JAVA_HOME`
   - 变量值：Java安装路径（例如：`C:\Program Files\Java\jdk-21`）
3. 编辑"Path"变量，添加：`%JAVA_HOME%\bin`
4. 点击"确定"保存

#### 验证Java安装
打开命令提示符，运行：
```cmd
java -version
javac -version
```

应该显示Java 21的版本信息。

## 构建插件

### 方法1：使用提供的批处理文件（推荐）
```cmd
build.bat
```
这个脚本会自动：
- 检查Node.js和Java环境
- 安装npm依赖
- 构建TypeScript代码
- 打包IDEA插件

### 方法2：手动构建
```cmd
# 1. 构建Web UI
cd src\main\resources\web
npm install
npm run build
cd ..\..\..\..

# 2. 构建IDEA插件
gradlew.bat buildPlugin
```

### 方法3：在IDEA中构建
1. 用IntelliJ IDEA打开项目
2. 先手动构建Web UI（执行方法2的第1步）
3. 打开Gradle工具窗口
4. 展开 `Tasks` -> `intellij`
5. 双击 `buildPlugin`

## 安装插件

### 1. 从磁盘安装
1. 打开IntelliJ IDEA
2. 进入 `File` -> `Settings` -> `Plugins`
3. 点击齿轮图标，选择 `Install Plugin from Disk...`
4. 选择构建生成的文件：`build/distributions/thinkphp-1.0-SNAPSHOT.zip`
5. 点击"OK"
6. 重启IDEA

### 2. 验证安装
重启IDEA后：
1. 在底部工具栏应该能看到"Symfony Dumper"标签
2. 点击标签打开工具窗口
3. 应该显示现代化的Web界面
4. 如果显示"JCEF不支持"错误，请参考故障排除部分

## 使用插件

### 1. 启动Web UI
1. 打开"Symfony Dumper"工具窗口
2. Web服务器会自动启动（默认端口9912）
3. 界面会显示连接状态和服务器信息
4. 绿色指示器表示连接正常

### 2. 测试连接
运行提供的测试脚本：
```cmd
php test-web.php
```

### 3. 配置Symfony应用
在Symfony项目中配置dump服务器：

```yaml
# config/packages/debug.yaml
when@dev:
    debug:
        dump_destination: "tcp://127.0.0.1:9912"
```

## 故障排除

### JCEF支持问题
1. **"JCEF不支持"错误**：
   - 确保使用IntelliJ IDEA 2020.1或更高版本
   - 检查是否使用JetBrains Runtime (JBR)
   - 尝试更新IDE到最新版本
   - 在IDEA中检查：Help -> About -> Runtime version

### Node.js相关问题
1. **Node.js未安装**：从官网下载安装Node.js LTS版本
2. **npm依赖安装失败**：
   - 删除`src/main/resources/web/node_modules`目录
   - 重新运行`npm install`
   - 检查网络连接和npm镜像设置
3. **TypeScript编译失败**：确保Node.js版本16+

### 构建问题
1. **JAVA_HOME未设置**：按照上面的步骤设置Java环境变量
2. **Java版本过低**：确保使用Java 21或更高版本
3. **Gradle构建失败**：检查网络连接，确保能访问Maven仓库

### 运行问题
1. **Web服务器启动失败**：
   - 检查端口9912是否被占用
   - 查看IDEA日志获取详细错误信息
   - 尝试重启IDEA
2. **WebSocket连接失败**：
   - 检查防火墙设置
   - 确认Node.js进程正在运行
   - 查看浏览器开发者工具的控制台

### 插件不显示
1. 确认插件已正确安装并重启IDEA
2. 检查IDEA版本兼容性（需要2020.1+）
3. 查看IDEA日志文件中的错误信息
4. 确认JCEF功能可用

## 开发模式

### 启动开发服务器
```cmd
dev-server.bat
```

### 在开发模式下运行插件
```cmd
gradlew.bat runIde
```

这将启动一个带有插件的IDEA实例用于测试。

### 调试Web UI
1. 在IDEA中右键点击Web UI区域
2. 选择"Inspect"打开开发者工具
3. 可以调试JavaScript代码和查看网络请求

## 支持

如果遇到问题，请检查：
1. IDEA的事件日志（Help -> Show Log in Explorer）
2. 浏览器开发者工具的控制台（在Web UI中右键 -> Inspect）
3. Node.js进程是否正常运行
4. 插件是否与当前IDEA版本兼容

### 获取帮助
- 查看项目README.md
- 提交GitHub Issue
- 检查IDEA和Node.js版本兼容性
