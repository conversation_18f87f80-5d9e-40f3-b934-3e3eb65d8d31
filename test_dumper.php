<?php
/**
 * Test script to send data to the Symfony Dumper Server
 * Run this script to test the IDEA plugin
 */

// Test data to send
$testData = [
    'message' => 'Hello from Symfony Dumper!',
    'timestamp' => date('Y-m-d H:i:s'),
    'data' => [
        'user_id' => 123,
        'username' => 'john_doe',
        'email' => '<EMAIL>',
        'roles' => ['ROLE_USER', 'ROLE_ADMIN']
    ],
    'debug_info' => [
        'file' => __FILE__,
        'line' => __LINE__,
        'memory_usage' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true)
    ]
];

// Convert to JSON
$jsonData = json_encode($testData, JSON_PRETTY_PRINT);

// Send to dumper server
$url = 'http://localhost:9912/';
$ch = curl_init($url);

curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($jsonData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_error($ch)) {
    echo "Error: " . curl_error($ch) . "\n";
} else {
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
    echo "Data sent successfully!\n";
}

curl_close($ch);

echo "\nSent data:\n";
echo $jsonData . "\n";
?>
