<?php
/**
 * 调试版本的Dumper测试脚本
 * 用于诊断连接和数据传输问题
 */

function testDumperConnection($host = 'localhost', $port = 9912) {
    echo "=== Symfony Dumper 连接测试 ===\n";
    echo "目标服务器: $host:$port\n\n";
    
    // 1. 测试端口是否开放
    echo "1. 检查端口连接...\n";
    $socket = @fsockopen($host, $port, $errno, $errstr, 5);
    if ($socket) {
        echo "✓ 端口 $port 可以连接\n";
        fclose($socket);
    } else {
        echo "✗ 端口 $port 连接失败: $errstr ($errno)\n";
        echo "请确保:\n";
        echo "  - IDEA插件已启动\n";
        echo "  - Dumper服务器正在运行\n";
        echo "  - 端口号正确\n";
        echo "  - 防火墙允许连接\n";
        return false;
    }
    
    // 2. 测试HTTP GET请求
    echo "\n2. 测试HTTP GET请求...\n";
    $url = "http://$host:$port/";
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'header' => "User-Agent: PHP-Dumper-Test/1.0\r\n"
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response !== false) {
        echo "✓ GET请求成功\n";
        echo "响应长度: " . strlen($response) . " 字节\n";
        if (strpos($response, 'Symfony Dumper Server') !== false) {
            echo "✓ 服务器响应正确\n";
        } else {
            echo "⚠ 服务器响应异常\n";
        }
    } else {
        echo "✗ GET请求失败\n";
        return false;
    }
    
    // 3. 测试HTTP POST请求
    echo "\n3. 测试HTTP POST请求...\n";
    
    $testData = [
        'test' => true,
        'message' => 'Debug test from PHP',
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => [
            'user_id' => 999,
            'action' => 'debug_test',
            'ip' => $_SERVER['SERVER_ADDR'] ?? 'unknown'
        ]
    ];
    
    $jsonData = json_encode($testData, JSON_PRETTY_PRINT);
    echo "发送数据:\n$jsonData\n\n";
    
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $jsonData,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData),
            'User-Agent: PHP-Dumper-Test/1.0'
        ],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_VERBOSE => false,
        CURLOPT_HEADER => true,
        CURLOPT_NOBODY => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    if ($error) {
        echo "✗ POST请求失败: $error\n";
        curl_close($ch);
        return false;
    }
    
    echo "HTTP状态码: $httpCode\n";
    
    if ($httpCode == 200) {
        echo "✓ POST请求成功\n";
        echo "响应:\n$response\n";
    } else {
        echo "✗ POST请求失败，状态码: $httpCode\n";
        echo "响应:\n$response\n";
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);
    
    // 4. 测试多次请求
    echo "\n4. 测试多次请求...\n";
    $successCount = 0;
    $totalRequests = 5;
    
    for ($i = 1; $i <= $totalRequests; $i++) {
        $testData['request_number'] = $i;
        $testData['timestamp'] = date('Y-m-d H:i:s');
        $jsonData = json_encode($testData);
        
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 5
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (!curl_error($ch) && $httpCode == 200) {
            $successCount++;
            echo "  请求 $i: ✓\n";
        } else {
            echo "  请求 $i: ✗ (错误: " . curl_error($ch) . ", 状态码: $httpCode)\n";
        }
        
        curl_close($ch);
        usleep(100000); // 100ms延迟
    }
    
    echo "\n成功率: $successCount/$totalRequests (" . round($successCount/$totalRequests*100, 1) . "%)\n";
    
    echo "\n=== 测试完成 ===\n";
    echo "如果所有测试都通过，数据应该已经显示在IDEA的Symfony Dumper工具窗口中。\n";
    echo "如果没有显示，请检查:\n";
    echo "1. IDEA插件是否正确安装\n";
    echo "2. Dumper工具窗口是否已打开\n";
    echo "3. 服务器是否已启动\n";
    echo "4. 查看IDEA的日志文件获取更多信息\n";
    
    return true;
}

// 运行测试
$host = $argv[1] ?? 'localhost';
$port = intval($argv[2] ?? 9912);

echo "PHP版本: " . PHP_VERSION . "\n";
echo "cURL版本: " . curl_version()['version'] . "\n\n";

testDumperConnection($host, $port);
?>
