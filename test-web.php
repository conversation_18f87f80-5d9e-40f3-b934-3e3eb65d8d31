<?php
/**
 * Web版本测试脚本
 * 用于测试Symfony Dumper Web UI
 */

function testWebDumper($host = 'localhost', $port = 9912) {
    echo "=== Symfony Dumper Web UI 测试 ===\n";
    echo "目标服务器: $host:$port\n\n";
    
    $url = "http://$host:$port/";
    
    // 测试数据
    $testCases = [
        [
            'name' => 'JSON数据测试',
            'data' => [
                'message' => 'Hello from Web UI test!',
                'timestamp' => date('Y-m-d H:i:s'),
                'user' => [
                    'id' => 123,
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'roles' => ['ROLE_USER', 'ROLE_ADMIN']
                ],
                'debug' => [
                    'memory' => memory_get_usage(true),
                    'peak_memory' => memory_get_peak_usage(true),
                    'execution_time' => microtime(true)
                ]
            ]
        ],
        [
            'name' => '错误信息测试',
            'data' => [
                'error' => [
                    'type' => 'ValidationException',
                    'message' => 'Invalid email format',
                    'code' => 422,
                    'file' => __FILE__,
                    'line' => __LINE__,
                    'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
                ]
            ]
        ],
        [
            'name' => '大数据测试',
            'data' => [
                'large_array' => array_fill(0, 100, [
                    'id' => rand(1, 1000),
                    'data' => str_repeat('test data ', 10),
                    'timestamp' => time()
                ])
            ]
        ],
        [
            'name' => 'HTML内容测试',
            'data' => '<html><head><title>Test</title></head><body><h1>HTML Test</h1><p>This is a test HTML content.</p></body></html>'
        ],
        [
            'name' => '纯文本测试',
            'data' => "This is a plain text dump.\nLine 2\nLine 3\nWith some special characters: àáâãäåæçèéêë"
        ]
    ];
    
    $successCount = 0;
    $totalTests = count($testCases);
    
    foreach ($testCases as $index => $testCase) {
        echo ($index + 1) . ". {$testCase['name']}...\n";
        
        $data = is_array($testCase['data']) ? json_encode($testCase['data'], JSON_PRETTY_PRINT) : $testCase['data'];
        $contentType = is_array($testCase['data']) ? 'application/json' : 'text/plain';
        
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => [
                "Content-Type: $contentType",
                'Content-Length: ' . strlen($data),
                'User-Agent: PHP-Web-Test/1.0'
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_HEADER => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        if ($error) {
            echo "   ✗ 失败: $error\n";
        } elseif ($httpCode == 200) {
            echo "   ✓ 成功 (HTTP $httpCode)\n";
            $successCount++;
        } else {
            echo "   ✗ 失败 (HTTP $httpCode)\n";
        }
        
        curl_close($ch);
        usleep(500000); // 500ms延迟
    }
    
    echo "\n=== 测试结果 ===\n";
    echo "成功: $successCount/$totalTests\n";
    echo "成功率: " . round($successCount / $totalTests * 100, 1) . "%\n\n";
    
    if ($successCount == $totalTests) {
        echo "🎉 所有测试通过！\n";
        echo "请检查IntelliJ IDEA中的Symfony Dumper工具窗口查看数据。\n";
    } else {
        echo "⚠️ 部分测试失败，请检查:\n";
        echo "1. Web服务器是否正在运行\n";
        echo "2. 端口是否正确\n";
        echo "3. 防火墙设置\n";
        echo "4. IDEA插件是否正确安装\n";
    }
    
    // 测试WebSocket连接
    echo "\n=== WebSocket连接测试 ===\n";
    testWebSocketConnection($host, $port);
}

function testWebSocketConnection($host, $port) {
    // 简单的WebSocket连接测试
    $socket = @fsockopen($host, $port, $errno, $errstr, 5);
    if ($socket) {
        echo "✓ 基础TCP连接成功\n";
        fclose($socket);
        
        // 测试HTTP升级到WebSocket
        $headers = [
            "GET / HTTP/1.1",
            "Host: $host:$port",
            "Upgrade: websocket",
            "Connection: Upgrade",
            "Sec-WebSocket-Key: " . base64_encode(random_bytes(16)),
            "Sec-WebSocket-Version: 13",
            "",
            ""
        ];
        
        $socket = fsockopen($host, $port, $errno, $errstr, 5);
        if ($socket) {
            fwrite($socket, implode("\r\n", $headers));
            $response = fread($socket, 1024);
            
            if (strpos($response, '101 Switching Protocols') !== false) {
                echo "✓ WebSocket升级成功\n";
            } else {
                echo "⚠️ WebSocket升级失败，但HTTP连接正常\n";
            }
            fclose($socket);
        }
    } else {
        echo "✗ TCP连接失败: $errstr ($errno)\n";
    }
}

// 运行测试
$host = $argv[1] ?? 'localhost';
$port = intval($argv[2] ?? 9912);

echo "PHP版本: " . PHP_VERSION . "\n";
echo "cURL版本: " . curl_version()['version'] . "\n\n";

testWebDumper($host, $port);
?>
