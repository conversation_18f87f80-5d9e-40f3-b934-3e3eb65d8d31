@echo off
echo Starting Symfony Dumper Development Server...

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Navigate to web directory
cd src\main\resources\web

REM Install dependencies if needed
if not exist node_modules (
    echo Installing Node.js dependencies...
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start development server
echo Starting development server...
echo.
echo Available commands:
echo - npm run dev     : Build once for development
echo - npm run watch   : Watch for changes and rebuild
echo - npm start       : Start the Node.js server
echo.

set /p choice="Choose mode (1=dev, 2=watch, 3=start): "

if "%choice%"=="1" (
    call npm run dev
) else if "%choice%"=="2" (
    echo Starting watch mode...
    echo Press Ctrl+C to stop
    call npm run watch
) else if "%choice%"=="3" (
    echo Starting Node.js server...
    echo Press Ctrl+C to stop
    call npm start
) else (
    echo Invalid choice, starting watch mode...
    call npm run watch
)

pause
