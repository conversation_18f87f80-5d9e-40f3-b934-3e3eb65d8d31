<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>com.topthink.thinkphp</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>Thinkphp</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor url="https://www.yourcompany.com">YourCompany</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    Symfony Dumper Server for IntelliJ IDEA.<br>
    <em>Provides a tool window to receive and display Symfony/Dumper output in a dedicated tab at the bottom of the IDE.</em>
  ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- Tool Window -->
        <toolWindow id="Symfony Dumper" secondary="true" anchor="bottom" 
                    factoryClass="com.topthink.thinkphp.DumperToolWindowFactory"/>
    </extensions>

    <!-- Application services -->
    <projectListeners>
        <listener class="com.topthink.thinkphp.DumperService" 
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
    </projectListeners>
</idea-plugin>
