{"name": "symfony-dumper-web", "version": "1.0.0", "description": "Web UI for Symfony Dumper IDEA Plugin", "main": "server.js", "scripts": {"build": "tsc && webpack --mode production", "dev": "tsc && webpack --mode development", "watch": "tsc --watch & webpack --mode development --watch", "start": "node dist/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "typescript": "^5.3.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "ts-loader": "^9.5.1", "html-webpack-plugin": "^5.5.4", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["symfony", "dumper", "idea", "plugin", "typescript"], "author": "TopThink", "license": "MIT"}