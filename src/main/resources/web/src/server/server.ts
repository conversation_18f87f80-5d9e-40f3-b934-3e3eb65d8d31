import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import bodyParser from 'body-parser';
import { DumperData, DumperServerConfig } from '../shared/types';

/**
 * TypeScript/Node.js版本的Symfony Dumper服务器
 */
export class DumperServer {
    private app: express.Application;
    private server: any;
    private io: SocketIOServer;
    private port: number;
    private isRunning: boolean = false;
    private dumpHistory: DumperData[] = [];
    private config: DumperServerConfig;

    constructor(config: DumperServerConfig = { port: 9912, host: '0.0.0.0' }) {
        this.config = config;
        this.port = config.port;
        this.app = express();
        this.server = createServer(this.app);
        this.io = new SocketIOServer(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });

        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketIO();
    }

    private setupMiddleware(): void {
        // CORS支持
        this.app.use(cors({
            origin: '*',
            methods: ['GET', 'POST', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization']
        }));

        // Body parser
        this.app.use(bodyParser.json({ limit: '10mb' }));
        this.app.use(bodyParser.text({ limit: '10mb' }));
        this.app.use(bodyParser.raw({ limit: '10mb' }));

        // 静态文件服务
        this.app.use(express.static('dist'));

        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} from ${req.ip}`);
            next();
        });
    }

    private setupRoutes(): void {
        // 主页面
        this.app.get('/', (req, res) => {
            res.sendFile('index.html', { root: 'dist' });
        });

        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                server: 'Symfony Dumper Server (TypeScript)',
                version: '1.0.0',
                uptime: process.uptime(),
                port: this.port,
                totalDumps: this.dumpHistory.length,
                isRunning: this.isRunning
            });
        });

        // 接收dump数据的主要端点
        this.app.post('/', (req, res) => {
            this.handleDumpRequest(req, res);
        });

        // API端点：获取历史数据
        this.app.get('/api/dumps', (req, res) => {
            const limit = parseInt(req.query.limit as string) || 100;
            const offset = parseInt(req.query.offset as string) || 0;
            
            const dumps = this.dumpHistory
                .slice(offset, offset + limit)
                .reverse(); // 最新的在前面

            res.json({
                dumps,
                total: this.dumpHistory.length,
                limit,
                offset
            });
        });

        // API端点：清空历史数据
        this.app.delete('/api/dumps', (req, res) => {
            this.dumpHistory = [];
            this.io.emit('dumpsCleared');
            res.json({ message: 'History cleared', total: 0 });
        });

        // API端点：获取单个dump
        this.app.get('/api/dumps/:id', (req, res) => {
            const dump = this.dumpHistory.find(d => d.id === req.params.id);
            if (dump) {
                res.json(dump);
            } else {
                res.status(404).json({ error: 'Dump not found' });
            }
        });
    }

    private setupSocketIO(): void {
        this.io.on('connection', (socket) => {
            console.log(`Client connected: ${socket.id}`);

            // 发送当前历史数据
            socket.emit('dumpHistory', this.dumpHistory);

            // 处理客户端请求
            socket.on('getDumps', (callback) => {
                callback(this.dumpHistory);
            });

            socket.on('clearDumps', () => {
                this.dumpHistory = [];
                this.io.emit('dumpsCleared');
            });

            socket.on('disconnect', () => {
                console.log(`Client disconnected: ${socket.id}`);
            });
        });
    }

    private handleDumpRequest(req: express.Request, res: express.Response): void {
        try {
            const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
            const userAgent = req.get('User-Agent') || 'unknown';
            const contentType = req.get('Content-Type') || 'text/plain';
            
            // 解析请求体
            let data: any = req.body;
            if (typeof data !== 'string') {
                data = JSON.stringify(data, null, 2);
            }

            // 检测数据类型
            const dataType = this.detectDataType(data, contentType);

            // 创建dump数据对象
            const dumpData: DumperData = {
                id: this.generateId(),
                timestamp: new Date(),
                data: data,
                type: dataType,
                source: `${clientIP} (${userAgent})`,
                contentType: contentType,
                size: Buffer.byteLength(data, 'utf8')
            };

            // 添加到历史记录
            this.dumpHistory.push(dumpData);

            // 限制历史记录大小
            if (this.dumpHistory.length > 1000) {
                this.dumpHistory = this.dumpHistory.slice(-1000);
            }

            // 通过WebSocket广播给所有连接的客户端
            this.io.emit('newDump', dumpData);

            console.log(`Received dump from ${clientIP}, type: ${dataType}, size: ${dumpData.size} bytes`);

            // 发送响应
            res.status(200).json({
                status: 'success',
                message: 'Dump received',
                id: dumpData.id,
                timestamp: dumpData.timestamp
            });

        } catch (error) {
            console.error('Error handling dump request:', error);
            res.status(500).json({
                status: 'error',
                message: 'Failed to process dump',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    private detectDataType(data: string, contentType: string): string {
        if (contentType.includes('application/json')) {
            return 'json';
        }
        if (contentType.includes('text/html') || contentType.includes('application/xml')) {
            return 'html/xml';
        }

        // 基于内容检测
        const trimmed = data.trim();
        if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
            try {
                JSON.parse(trimmed);
                return 'json';
            } catch {
                return 'text';
            }
        }
        if (trimmed.startsWith('<')) {
            return 'html/xml';
        }
        if (trimmed.includes('Symfony\\Component\\VarDumper')) {
            return 'symfony-dump';
        }

        return 'text';
    }

    private generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    public start(): Promise<boolean> {
        return new Promise((resolve) => {
            try {
                this.server.listen(this.port, this.config.host, () => {
                    this.isRunning = true;
                    console.log(`🚀 Symfony Dumper Server started on ${this.config.host}:${this.port}`);
                    console.log(`📊 Dashboard: http://localhost:${this.port}`);
                    console.log(`🔗 API: http://localhost:${this.port}/api/dumps`);
                    resolve(true);
                });
            } catch (error) {
                console.error('Failed to start server:', error);
                resolve(false);
            }
        });
    }

    public stop(): Promise<void> {
        return new Promise((resolve) => {
            if (this.server && this.isRunning) {
                this.server.close(() => {
                    this.isRunning = false;
                    console.log('Symfony Dumper Server stopped');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    public getStatus() {
        return {
            isRunning: this.isRunning,
            port: this.port,
            host: this.config.host,
            totalDumps: this.dumpHistory.length,
            uptime: process.uptime()
        };
    }
}

// 如果直接运行此文件，启动服务器
if (require.main === module) {
    const server = new DumperServer();
    server.start().then((success) => {
        if (!success) {
            process.exit(1);
        }
    });

    // 优雅关闭
    process.on('SIGINT', async () => {
        console.log('\nShutting down server...');
        await server.stop();
        process.exit(0);
    });
}
