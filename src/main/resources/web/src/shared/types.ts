/**
 * 共享类型定义
 */

export interface DumperData {
    id: string;
    timestamp: Date;
    data: string;
    type: string;
    source?: string;
    contentType?: string;
    size?: number;
    line?: number;
    file?: string;
}

export interface DumperServerConfig {
    port: number;
    host: string;
    maxHistorySize?: number;
    enableCors?: boolean;
}

export interface ServerStatus {
    isRunning: boolean;
    port: number;
    host: string;
    totalDumps: number;
    uptime: number;
}

export interface DumpResponse {
    status: 'success' | 'error';
    message: string;
    id?: string;
    timestamp?: Date;
    error?: string;
}

export interface DumpsListResponse {
    dumps: DumperData[];
    total: number;
    limit: number;
    offset: number;
}

export interface HealthResponse {
    status: string;
    server: string;
    version: string;
    uptime: number;
    port: number;
    totalDumps: number;
    isRunning: boolean;
}

// WebSocket事件类型
export interface ServerToClientEvents {
    newDump: (dump: DumperData) => void;
    dumpHistory: (dumps: DumperData[]) => void;
    dumpsCleared: () => void;
    serverStatus: (status: ServerStatus) => void;
}

export interface ClientToServerEvents {
    getDumps: (callback: (dumps: DumperData[]) => void) => void;
    clearDumps: () => void;
    getStatus: (callback: (status: ServerStatus) => void) => void;
}

// UI相关类型
export interface UIState {
    isConnected: boolean;
    serverStatus: ServerStatus | null;
    dumps: DumperData[];
    selectedDump: DumperData | null;
    filter: string;
    sortBy: 'timestamp' | 'type' | 'size';
    sortOrder: 'asc' | 'desc';
}

export interface FilterOptions {
    type?: string;
    dateFrom?: Date;
    dateTo?: Date;
    source?: string;
    minSize?: number;
    maxSize?: number;
}

// 工具函数类型
export type DataFormatter = (data: string, type: string) => string;
export type TimeFormatter = (date: Date) => string;
export type SizeFormatter = (bytes: number) => string;
