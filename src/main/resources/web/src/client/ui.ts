import { DumperData, UIState, ServerStatus } from '../shared/types';
import { formatData, formatTime, formatSize } from './formatters';

/**
 * UI管理类
 */
export class DumperUI {
    private state: UIState;
    private eventHandler: (event: string, data?: any) => void;
    private elements: { [key: string]: HTMLElement } = {};

    constructor(state: UIState, eventHandler: (event: string, data?: any) => void) {
        this.state = state;
        this.eventHandler = eventHandler;
    }

    public render(): void {
        this.createLayout();
        this.bindEvents();
    }

    private createLayout(): void {
        const app = document.getElementById('app');
        if (!app) return;

        app.innerHTML = `
            <div class="dumper-container">
                <!-- Header -->
                <header class="dumper-header">
                    <div class="header-left">
                        <h1>🔍 Symfony Dumper</h1>
                        <div class="connection-status" id="connectionStatus">
                            <span class="status-indicator"></span>
                            <span class="status-text">Connecting...</span>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="server-info" id="serverInfo"></div>
                        <button class="btn btn-secondary" id="refreshBtn">🔄 Refresh</button>
                        <button class="btn btn-primary" id="exportBtn">📥 Export</button>
                        <button class="btn btn-danger" id="clearBtn">🗑️ Clear</button>
                    </div>
                </header>

                <!-- Toolbar -->
                <div class="dumper-toolbar">
                    <div class="toolbar-left">
                        <input type="text" id="filterInput" placeholder="Filter dumps..." class="filter-input">
                        <select id="sortSelect" class="sort-select">
                            <option value="timestamp-desc">Latest First</option>
                            <option value="timestamp-asc">Oldest First</option>
                            <option value="type-asc">Type A-Z</option>
                            <option value="type-desc">Type Z-A</option>
                            <option value="size-desc">Largest First</option>
                            <option value="size-asc">Smallest First</option>
                        </select>
                    </div>
                    <div class="toolbar-right">
                        <span class="dumps-count" id="dumpsCount">0 dumps</span>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="dumper-content">
                    <!-- Dumps List -->
                    <div class="dumps-panel">
                        <div class="dumps-list" id="dumpsList">
                            <div class="empty-state">
                                <div class="empty-icon">📭</div>
                                <h3>No dumps yet</h3>
                                <p>Start your Symfony application and use dump() to see data here.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Dump Detail -->
                    <div class="detail-panel">
                        <div class="detail-header">
                            <h3>Dump Details</h3>
                            <div class="detail-actions">
                                <button class="btn btn-sm" id="copyBtn">📋 Copy</button>
                                <button class="btn btn-sm" id="formatBtn">🎨 Format</button>
                            </div>
                        </div>
                        <div class="detail-content" id="detailContent">
                            <div class="empty-detail">
                                <p>Select a dump to view details</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="notifications" id="notifications"></div>
            </div>
        `;

        // 缓存重要元素
        this.elements = {
            connectionStatus: document.getElementById('connectionStatus')!,
            serverInfo: document.getElementById('serverInfo')!,
            filterInput: document.getElementById('filterInput')!,
            sortSelect: document.getElementById('sortSelect')!,
            dumpsCount: document.getElementById('dumpsCount')!,
            dumpsList: document.getElementById('dumpsList')!,
            detailContent: document.getElementById('detailContent')!,
            notifications: document.getElementById('notifications')!
        };
    }

    private bindEvents(): void {
        // 过滤器
        this.elements.filterInput.addEventListener('input', (e) => {
            const target = e.target as HTMLInputElement;
            this.eventHandler('filterChange', target.value);
        });

        // 排序
        this.elements.sortSelect.addEventListener('change', (e) => {
            const target = e.target as HTMLSelectElement;
            const [sortBy, sortOrder] = target.value.split('-');
            this.eventHandler('sortChange', { sortBy, sortOrder });
        });

        // 按钮事件
        document.getElementById('refreshBtn')?.addEventListener('click', () => {
            this.eventHandler('refreshData');
        });

        document.getElementById('exportBtn')?.addEventListener('click', () => {
            this.eventHandler('exportDumps');
        });

        document.getElementById('clearBtn')?.addEventListener('click', () => {
            this.eventHandler('clearDumps');
        });

        document.getElementById('copyBtn')?.addEventListener('click', () => {
            this.copySelectedDump();
        });

        document.getElementById('formatBtn')?.addEventListener('click', () => {
            this.formatSelectedDump();
        });
    }

    public updateConnectionStatus(connected: boolean): void {
        const status = this.elements.connectionStatus;
        const indicator = status.querySelector('.status-indicator') as HTMLElement;
        const text = status.querySelector('.status-text') as HTMLElement;

        if (connected) {
            indicator.className = 'status-indicator connected';
            text.textContent = 'Connected';
        } else {
            indicator.className = 'status-indicator disconnected';
            text.textContent = 'Disconnected';
        }
    }

    public updateServerStatus(status: ServerStatus): void {
        this.elements.serverInfo.innerHTML = `
            <div class="server-status">
                <span>Port: ${status.port}</span>
                <span>Dumps: ${status.totalDumps}</span>
                <span>Uptime: ${this.formatUptime(status.uptime)}</span>
            </div>
        `;
    }

    public updateDumpsList(dumps: DumperData[]): void {
        this.elements.dumpsCount.textContent = `${dumps.length} dumps`;

        if (dumps.length === 0) {
            this.elements.dumpsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📭</div>
                    <h3>No dumps found</h3>
                    <p>Try adjusting your filter or clear it to see all dumps.</p>
                </div>
            `;
            return;
        }

        const dumpsHtml = dumps.map(dump => `
            <div class="dump-item" data-id="${dump.id}" onclick="window.selectDump('${dump.id}')">
                <div class="dump-header">
                    <span class="dump-type ${dump.type}">${dump.type}</span>
                    <span class="dump-time">${formatTime(dump.timestamp)}</span>
                    <span class="dump-size">${formatSize(dump.size || 0)}</span>
                </div>
                <div class="dump-preview">${this.truncateText(dump.data, 100)}</div>
                ${dump.source ? `<div class="dump-source">${dump.source}</div>` : ''}
            </div>
        `).join('');

        this.elements.dumpsList.innerHTML = dumpsHtml;

        // 绑定选择事件
        (window as any).selectDump = (id: string) => {
            const dump = dumps.find(d => d.id === id);
            if (dump) {
                this.eventHandler('selectDump', dump);
            }
        };
    }

    public updateDumpDetail(dump: DumperData | null): void {
        if (!dump) {
            this.elements.detailContent.innerHTML = `
                <div class="empty-detail">
                    <p>Select a dump to view details</p>
                </div>
            `;
            return;
        }

        const formattedData = formatData(dump.data, dump.type);
        
        this.elements.detailContent.innerHTML = `
            <div class="dump-detail">
                <div class="detail-meta">
                    <div class="meta-item">
                        <label>ID:</label>
                        <span>${dump.id}</span>
                    </div>
                    <div class="meta-item">
                        <label>Type:</label>
                        <span class="type-badge ${dump.type}">${dump.type}</span>
                    </div>
                    <div class="meta-item">
                        <label>Time:</label>
                        <span>${dump.timestamp.toLocaleString()}</span>
                    </div>
                    <div class="meta-item">
                        <label>Size:</label>
                        <span>${formatSize(dump.size || 0)}</span>
                    </div>
                    ${dump.source ? `
                        <div class="meta-item">
                            <label>Source:</label>
                            <span>${dump.source}</span>
                        </div>
                    ` : ''}
                </div>
                <div class="detail-data">
                    <pre><code class="language-${dump.type}">${formattedData}</code></pre>
                </div>
            </div>
        `;
    }

    public highlightSelectedDump(id: string): void {
        // 移除之前的选中状态
        document.querySelectorAll('.dump-item.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // 添加新的选中状态
        const selectedItem = document.querySelector(`[data-id="${id}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
            selectedItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    public showNotification(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        `;

        this.elements.notifications.appendChild(notification);

        // 自动关闭
        setTimeout(() => {
            notification.remove();
        }, 5000);

        // 手动关闭
        notification.querySelector('.notification-close')?.addEventListener('click', () => {
            notification.remove();
        });
    }

    private copySelectedDump(): void {
        if (!this.state.selectedDump) return;

        navigator.clipboard.writeText(this.state.selectedDump.data).then(() => {
            this.showNotification('Dump data copied to clipboard', 'success');
        }).catch(() => {
            this.showNotification('Failed to copy data', 'error');
        });
    }

    private formatSelectedDump(): void {
        if (!this.state.selectedDump) return;

        try {
            const formatted = formatData(this.state.selectedDump.data, this.state.selectedDump.type);
            this.updateDumpDetail({ ...this.state.selectedDump, data: formatted });
            this.showNotification('Data formatted', 'success');
        } catch (error) {
            this.showNotification('Failed to format data', 'error');
        }
    }

    private truncateText(text: string, maxLength: number): string {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    private formatUptime(seconds: number): string {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }
}
