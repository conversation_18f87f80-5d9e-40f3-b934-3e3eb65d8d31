<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Symfony Dumper - IntelliJ IDEA Plugin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1e1e1e;
            color: #d4d4d4;
            height: 100vh;
            overflow: hidden;
        }

        .dumper-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Header */
        .dumper-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-left h1 {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #666;
        }

        .status-indicator.connected {
            background: #4caf50;
        }

        .status-indicator.disconnected {
            background: #f44336;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .server-info {
            font-size: 12px;
            color: #999;
        }

        .server-status {
            display: flex;
            gap: 12px;
        }

        /* Buttons */
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #007acc;
            color: white;
        }

        .btn-primary:hover {
            background: #005a9e;
        }

        .btn-secondary {
            background: #5a5a5a;
            color: white;
        }

        .btn-secondary:hover {
            background: #6a6a6a;
        }

        .btn-danger {
            background: #d73a49;
            color: white;
        }

        .btn-danger:hover {
            background: #b92534;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 11px;
        }

        /* Toolbar */
        .dumper-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            background: #252526;
            border-bottom: 1px solid #3e3e42;
        }

        .toolbar-left {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .filter-input {
            padding: 6px 12px;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            background: #1e1e1e;
            color: #d4d4d4;
            font-size: 12px;
            width: 200px;
        }

        .sort-select {
            padding: 6px 12px;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            background: #1e1e1e;
            color: #d4d4d4;
            font-size: 12px;
        }

        .dumps-count {
            font-size: 12px;
            color: #999;
        }

        /* Main Content */
        .dumper-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .dumps-panel {
            width: 40%;
            border-right: 1px solid #3e3e42;
            display: flex;
            flex-direction: column;
        }

        .dumps-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .dump-item {
            padding: 12px;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .dump-item:hover {
            background: #2d2d30;
            border-color: #007acc;
        }

        .dump-item.selected {
            background: #264f78;
            border-color: #007acc;
        }

        .dump-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .dump-type {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .dump-type.json {
            background: #4caf50;
            color: white;
        }

        .dump-type.text {
            background: #2196f3;
            color: white;
        }

        .dump-type.symfony-dump {
            background: #ff9800;
            color: white;
        }

        .dump-type.html\/xml {
            background: #9c27b0;
            color: white;
        }

        .dump-time {
            font-size: 11px;
            color: #999;
        }

        .dump-size {
            font-size: 11px;
            color: #999;
        }

        .dump-preview {
            font-size: 12px;
            color: #d4d4d4;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .dump-source {
            font-size: 10px;
            color: #666;
            margin-top: 4px;
        }

        /* Detail Panel */
        .detail-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }

        .detail-header h3 {
            font-size: 14px;
            font-weight: 600;
        }

        .detail-actions {
            display: flex;
            gap: 8px;
        }

        .detail-content {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .detail-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background: #2d2d30;
            border-radius: 4px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .meta-item label {
            font-size: 11px;
            color: #999;
            text-transform: uppercase;
            font-weight: 600;
        }

        .type-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-block;
        }

        .detail-data {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            overflow: hidden;
        }

        .detail-data pre {
            margin: 0;
            padding: 16px;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.5;
        }

        /* Empty States */
        .empty-state, .empty-detail {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: #666;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state h3 {
            margin-bottom: 8px;
            color: #999;
        }

        /* Notifications */
        .notifications {
            position: fixed;
            top: 16px;
            right: 16px;
            z-index: 1000;
        }

        .notification {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 4px;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .notification.info {
            background: #2196f3;
            color: white;
        }

        .notification.success {
            background: #4caf50;
            color: white;
        }

        .notification.warning {
            background: #ff9800;
            color: white;
        }

        .notification.error {
            background: #f44336;
            color: white;
        }

        .notification-close {
            background: none;
            border: none;
            color: inherit;
            font-size: 16px;
            cursor: pointer;
            margin-left: 12px;
        }

        /* Syntax Highlighting */
        .json-key { color: #9cdcfe; }
        .json-string { color: #ce9178; }
        .json-number { color: #b5cea8; }
        .json-boolean { color: #569cd6; }
        .json-null { color: #569cd6; }
        .xml-tag { color: #4ec9b0; }
        .xml-attr { color: #92c5f8; }

        /* Scrollbars */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #1e1e1e;
        }

        ::-webkit-scrollbar-thumb {
            background: #424242;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="loading">
            <div class="loading-spinner"></div>
            <p>Loading Symfony Dumper...</p>
        </div>
    </div>
</body>
</html>
