import { io, Socket } from 'socket.io-client';
import { DumperData, UIState, ServerStatus } from '../shared/types';
import { DumperUI } from './ui';
import { DataFormatter } from './formatters';

/**
 * 主要的客户端应用类
 */
class DumperApp {
    private socket: Socket;
    private ui: DumperUI;
    private state: UIState;
    private formatter: DataFormatter;

    constructor() {
        this.state = {
            isConnected: false,
            serverStatus: null,
            dumps: [],
            selectedDump: null,
            filter: '',
            sortBy: 'timestamp',
            sortOrder: 'desc'
        };

        this.formatter = new DataFormatter();
        this.ui = new DumperUI(this.state, this.handleUIEvents.bind(this));
        this.socket = io();

        this.setupSocketEvents();
        this.init();
    }

    private setupSocketEvents(): void {
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.state.isConnected = true;
            this.ui.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.state.isConnected = false;
            this.ui.updateConnectionStatus(false);
        });

        this.socket.on('newDump', (dump: DumperData) => {
            console.log('New dump received:', dump.id);
            this.addDump(dump);
        });

        this.socket.on('dumpHistory', (dumps: DumperData[]) => {
            console.log('Dump history received:', dumps.length, 'items');
            this.state.dumps = dumps;
            this.ui.updateDumpsList(this.getFilteredDumps());
        });

        this.socket.on('dumpsCleared', () => {
            console.log('Dumps cleared');
            this.state.dumps = [];
            this.state.selectedDump = null;
            this.ui.updateDumpsList([]);
            this.ui.updateDumpDetail(null);
        });

        this.socket.on('serverStatus', (status: ServerStatus) => {
            this.state.serverStatus = status;
            this.ui.updateServerStatus(status);
        });
    }

    private handleUIEvents(event: string, data?: any): void {
        switch (event) {
            case 'selectDump':
                this.selectDump(data);
                break;
            case 'clearDumps':
                this.clearDumps();
                break;
            case 'filterChange':
                this.updateFilter(data);
                break;
            case 'sortChange':
                this.updateSort(data.sortBy, data.sortOrder);
                break;
            case 'exportDumps':
                this.exportDumps();
                break;
            case 'refreshData':
                this.refreshData();
                break;
        }
    }

    private addDump(dump: DumperData): void {
        // 转换时间戳
        dump.timestamp = new Date(dump.timestamp);
        
        this.state.dumps.unshift(dump); // 添加到开头
        
        // 限制历史记录大小
        if (this.state.dumps.length > 1000) {
            this.state.dumps = this.state.dumps.slice(0, 1000);
        }

        this.ui.updateDumpsList(this.getFilteredDumps());
        
        // 如果没有选中的dump，自动选中最新的
        if (!this.state.selectedDump) {
            this.selectDump(dump);
        }

        // 显示通知
        this.ui.showNotification(`New dump received: ${dump.type}`, 'info');
    }

    private selectDump(dump: DumperData): void {
        this.state.selectedDump = dump;
        this.ui.updateDumpDetail(dump);
        this.ui.highlightSelectedDump(dump.id);
    }

    private clearDumps(): void {
        if (confirm('Are you sure you want to clear all dumps?')) {
            this.socket.emit('clearDumps');
        }
    }

    private updateFilter(filter: string): void {
        this.state.filter = filter;
        this.ui.updateDumpsList(this.getFilteredDumps());
    }

    private updateSort(sortBy: 'timestamp' | 'type' | 'size', sortOrder: 'asc' | 'desc'): void {
        this.state.sortBy = sortBy;
        this.state.sortOrder = sortOrder;
        this.ui.updateDumpsList(this.getFilteredDumps());
    }

    private getFilteredDumps(): DumperData[] {
        let filtered = [...this.state.dumps];

        // 应用过滤器
        if (this.state.filter) {
            const filter = this.state.filter.toLowerCase();
            filtered = filtered.filter(dump => 
                dump.data.toLowerCase().includes(filter) ||
                dump.type.toLowerCase().includes(filter) ||
                (dump.source && dump.source.toLowerCase().includes(filter))
            );
        }

        // 应用排序
        filtered.sort((a, b) => {
            let comparison = 0;
            
            switch (this.state.sortBy) {
                case 'timestamp':
                    comparison = a.timestamp.getTime() - b.timestamp.getTime();
                    break;
                case 'type':
                    comparison = a.type.localeCompare(b.type);
                    break;
                case 'size':
                    comparison = (a.size || 0) - (b.size || 0);
                    break;
            }

            return this.state.sortOrder === 'desc' ? -comparison : comparison;
        });

        return filtered;
    }

    private exportDumps(): void {
        const dumps = this.getFilteredDumps();
        const dataStr = JSON.stringify(dumps, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `symfony-dumps-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.ui.showNotification('Dumps exported successfully', 'success');
    }

    private refreshData(): void {
        this.socket.emit('getDumps', (dumps: DumperData[]) => {
            this.state.dumps = dumps.map(dump => ({
                ...dump,
                timestamp: new Date(dump.timestamp)
            }));
            this.ui.updateDumpsList(this.getFilteredDumps());
        });
    }

    private async init(): Promise<void> {
        try {
            // 初始化UI
            this.ui.render();
            
            // 获取服务器状态
            const response = await fetch('/health');
            if (response.ok) {
                const health = await response.json();
                this.state.serverStatus = {
                    isRunning: health.isRunning,
                    port: health.port,
                    host: 'localhost',
                    totalDumps: health.totalDumps,
                    uptime: health.uptime
                };
                this.ui.updateServerStatus(this.state.serverStatus);
            }

            console.log('Dumper app initialized');
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.ui.showNotification('Failed to connect to server', 'error');
        }
    }
}

// 启动应用
document.addEventListener('DOMContentLoaded', () => {
    new DumperApp();
});
