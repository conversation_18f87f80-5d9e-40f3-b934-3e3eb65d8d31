/**
 * 数据格式化工具
 */

export class DataFormatter {
    public formatData(data: string, type: string): string {
        switch (type) {
            case 'json':
                return this.formatJSON(data);
            case 'html/xml':
                return this.formatXML(data);
            case 'symfony-dump':
                return this.formatSymfonyDump(data);
            default:
                return data;
        }
    }

    private formatJSON(data: string): string {
        try {
            const parsed = JSON.parse(data);
            return JSON.stringify(parsed, null, 2);
        } catch {
            return data;
        }
    }

    private formatXML(data: string): string {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(data, 'text/xml');
            const serializer = new XMLSerializer();
            return this.indentXML(serializer.serializeToString(xmlDoc));
        } catch {
            return data;
        }
    }

    private formatSymfonyDump(data: string): string {
        // 简单的Symfony dump格式化
        return data
            .replace(/\\n/g, '\n')
            .replace(/\\t/g, '\t')
            .replace(/\\"/g, '"');
    }

    private indentXML(xml: string): string {
        const PADDING = '  ';
        const reg = /(>)(<)(\/*)/g;
        let formatted = xml.replace(reg, '$1\n$2$3');
        let pad = 0;
        
        return formatted.split('\n').map(line => {
            let indent = 0;
            if (line.match(/.+<\/\w[^>]*>$/)) {
                indent = 0;
            } else if (line.match(/^<\/\w/) && pad > 0) {
                pad -= 1;
            } else if (line.match(/^<\w[^>]*[^\/]>.*$/)) {
                indent = 1;
            }
            
            const padding = PADDING.repeat(pad);
            pad += indent;
            return padding + line;
        }).join('\n');
    }
}

// 导出便捷函数
export function formatData(data: string, type: string): string {
    const formatter = new DataFormatter();
    return formatter.formatData(data, type);
}

export function formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

export function formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

export function formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`;
}

export function highlightSyntax(code: string, language: string): string {
    // 简单的语法高亮（在实际项目中可以使用Prism.js或highlight.js）
    switch (language) {
        case 'json':
            return highlightJSON(code);
        case 'html':
        case 'xml':
            return highlightXML(code);
        default:
            return escapeHtml(code);
    }
}

function highlightJSON(json: string): string {
    return json
        .replace(/(".*?")\s*:/g, '<span class="json-key">$1</span>:')
        .replace(/:\s*(".*?")/g, ': <span class="json-string">$1</span>')
        .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
        .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>')
        .replace(/:\s*(\d+\.?\d*)/g, ': <span class="json-number">$1</span>');
}

function highlightXML(xml: string): string {
    return xml
        .replace(/(&lt;\/?)(\w+)(.*?&gt;)/g, '$1<span class="xml-tag">$2</span>$3')
        .replace(/(\w+)=(".*?")/g, '<span class="xml-attr">$1</span>=$2');
}

function escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
