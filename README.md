# Symfony Dumper IDEA Plugin

这是一个IntelliJ IDEA插件，用于接收和显示Symfony/Dumper的输出结果。支持两种UI模式：现代化的Web界面和传统的Swing界面。

## 🎨 双UI模式

### Web UI (推荐)
- 🌐 基于TypeScript/HTML/CSS的现代化界面
- ⚡ 实时WebSocket连接，即时显示dump数据
- 🎯 语法高亮和数据格式化
- 📱 响应式设计，支持不同窗口大小
- 🔍 强大的过滤和搜索功能
- 📊 数据统计和可视化

### Classic UI (备用)
- 🖥️ 传统Kotlin/Swing界面
- 🔧 稳定可靠，兼容性好
- 📋 表格形式显示数据
- 🎛️ 简单直观的操作界面

## 功能特性

- 在IDEA底部添加"Symfony Dumper"工具窗口
- 启动HTTP服务器接收dump数据（支持所有IP连接）
- 实时显示接收到的数据
- 支持数据历史记录和搜索
- 可配置服务器端口
- 支持清空历史数据
- 数据导出功能
- 多种数据格式支持（JSON、XML、HTML、纯文本）

## 安装和使用

### 1. 构建插件

```bash
./gradlew buildPlugin
```

### 2. 安装插件

1. 打开IntelliJ IDEA
2. 进入 `File` -> `Settings` -> `Plugins`
3. 点击齿轮图标，选择 `Install Plugin from Disk...`
4. 选择构建生成的插件文件 `build/distributions/thinkphp-1.0-SNAPSHOT.zip`
5. 重启IDEA

### 3. 使用插件

1. 安装插件后，在IDEA底部会出现"Symfony Dumper"标签页
2. 点击标签页打开工具窗口
3. 设置端口号（默认9912）
4. 点击"Start Server"启动服务器
5. 服务器启动后，可以接收来自Symfony/Dumper的数据

### 4. 配置Symfony应用

在你的Symfony应用中配置dump服务器：

```yaml
# config/packages/debug.yaml
when@dev:
    debug:
        dump_destination: "tcp://127.0.0.1:9912"
```

或者在代码中直接使用：

```php
<?php
// 发送数据到dump服务器
$data = ['key' => 'value', 'array' => [1, 2, 3]];
dump($data);
```

### 5. 测试插件

使用提供的测试脚本：

```bash
php test_dumper.php
```

## 工具窗口功能

### 工具栏
- **Port**: 设置服务器监听端口
- **Start/Stop Server**: 启动或停止服务器
- **Clear**: 清空历史数据
- **状态显示**: 显示服务器运行状态

### 数据表格
- **Time**: 接收数据的时间
- **Type**: 数据类型
- **Preview**: 数据预览（前100个字符）
- **Source**: 数据来源文件和行号

### 详细信息面板
- 点击表格中的任意行，下方会显示完整的dump数据
- 支持滚动查看长内容

## 开发说明

### 项目结构

```
src/main/kotlin/com/topthink/thinkphp/
├── DumperData.kt          # 数据模型
├── DumperServer.kt        # HTTP服务器
├── DumperPanel.kt         # UI面板
├── DumperToolWindow.kt    # 工具窗口
└── DumperService.kt       # 服务管理
```

### 主要组件

1. **DumperServer**: 基于Java HttpServer的HTTP服务器，监听指定端口接收POST请求
2. **DumperPanel**: Swing UI面板，包含数据表格和详细信息显示
3. **DumperToolWindow**: IDEA工具窗口工厂类
4. **DumperService**: 项目生命周期管理服务

### 扩展功能

可以考虑添加的功能：
- 支持更多数据格式（XML, HTML等）
- 数据过滤和搜索
- 导出数据功能
- 自定义主题和样式
- 数据统计和分析

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
