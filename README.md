# Symfony Dumper IDEA Plugin (Web UI)

这是一个IntelliJ IDEA插件，用于接收和显示Symfony/Dumper的输出结果。采用现代化的Web技术构建，提供优秀的用户体验。

## 🌟 特性

### 🌐 现代化Web界面
- 基于TypeScript/HTML/CSS的现代化界面
- 深色主题，完美融入IDEA环境
- 响应式设计，支持不同窗口大小
- 平滑动画和过渡效果

### ⚡ 实时数据处理
- WebSocket连接，零延迟数据更新
- 自动检测数据类型（JSON、XML、HTML、纯文本）
- 语法高亮和数据格式化
- 智能数据预览和详细视图

### 🔍 强大的交互功能
- 实时搜索和过滤
- 多种排序选项（时间、类型、大小）
- 一键复制和数据导出
- 连接状态监控和错误处理

### 📊 数据管理
- 历史记录管理（最多1000条）
- 数据统计和可视化
- 批量操作支持
- 性能优化的虚拟滚动

## 🛠️ 技术栈

- **后端**: Node.js + Express + Socket.IO (TypeScript)
- **前端**: TypeScript + HTML5 + CSS3
- **构建**: Webpack + TypeScript编译器
- **集成**: JCEF (Java Chromium Embedded Framework)

## 📋 前置要求

- IntelliJ IDEA 2020.1+ (支持JCEF)
- Node.js 16+ 和 npm
- Java 21+ (用于构建插件)

## 🚀 安装和使用

### 1. 构建插件

```bash
# 使用构建脚本（推荐）
build.bat

# 或手动构建
cd src/main/resources/web
npm install
npm run build
cd ../../../..
./gradlew buildPlugin
```

### 2. 安装插件

1. 打开IntelliJ IDEA
2. 进入 `File` → `Settings` → `Plugins`
3. 点击齿轮图标，选择 `Install Plugin from Disk...`
4. 选择构建生成的插件文件 `build/distributions/thinkphp-1.0-SNAPSHOT.zip`
5. 重启IDEA

### 3. 使用插件

1. 安装插件后，在IDEA底部会出现"Symfony Dumper"标签页
2. 点击标签页打开Web UI界面
3. Web服务器会自动启动（默认端口9912）
4. 界面会显示连接状态和服务器信息

### 4. 配置Symfony应用

在你的Symfony应用中配置dump服务器：

```yaml
# config/packages/debug.yaml
when@dev:
    debug:
        dump_destination: "tcp://127.0.0.1:9912"
```

或者在代码中直接使用：

```php
<?php
// 发送数据到dump服务器
$data = ['key' => 'value', 'array' => [1, 2, 3]];
dump($data);
```

### 5. 测试插件

使用提供的测试脚本：

```bash
php test-web.php
```

## 🎨 Web UI功能

### 📊 主界面
- **连接状态**: 实时显示WebSocket连接状态
- **服务器信息**: 端口、运行时间、数据统计
- **操作按钮**: 刷新、导出、清空数据

### 🔍 数据管理
- **实时列表**: 显示所有接收到的dump数据
- **智能过滤**: 支持内容、类型、来源过滤
- **多种排序**: 按时间、类型、大小排序
- **数据预览**: 鼠标悬停显示完整内容

### 📝 详细视图
- **语法高亮**: JSON、XML、HTML自动高亮
- **格式化显示**: 自动美化数据格式
- **元数据信息**: 显示时间、大小、来源等
- **操作功能**: 复制、格式化、导出

### 🔔 通知系统
- **实时提醒**: 新数据到达通知
- **状态反馈**: 操作成功/失败提示
- **错误处理**: 连接异常自动重连

## 🛠️ 开发说明

### 项目结构

```
src/main/
├── kotlin/com/topthink/thinkphp/
│   ├── WebDumperPanel.kt      # JCEF集成
│   ├── DumperToolWindow.kt    # 工具窗口
│   └── DumperService.kt       # 生命周期管理
└── resources/web/
    ├── src/
    │   ├── server/            # Node.js服务器
    │   ├── client/            # TypeScript前端
    │   └── shared/            # 共享类型定义
    ├── package.json           # 依赖配置
    ├── tsconfig.json          # TypeScript配置
    └── webpack.config.js      # 构建配置
```

### 主要组件

1. **WebDumperPanel**: JCEF浏览器集成，负责启动Node.js服务器
2. **Node.js Server**: Express + Socket.IO，处理HTTP请求和WebSocket连接
3. **TypeScript Frontend**: 现代化Web界面，实时数据展示
4. **DumperToolWindow**: IDEA工具窗口工厂类

### 🔧 开发模式

```bash
# 启动开发服务器
dev-server.bat

# 或手动启动
cd src/main/resources/web
npm run watch    # 监听文件变化
npm start        # 启动Node.js服务器
```

### 🚀 API端点

- `GET /` - Web UI主页
- `POST /` - 接收dump数据
- `GET /health` - 服务器状态
- `GET /api/dumps` - 获取历史数据
- `DELETE /api/dumps` - 清空数据
- `WebSocket` - 实时数据推送

## 🔧 故障排除

### JCEF不支持
如果看到"JCEF不支持"的错误：
1. 确保使用IntelliJ IDEA 2020.1+
2. 检查是否使用JetBrains Runtime (JBR)
3. 尝试更新IDE到最新版本

### Node.js相关问题
1. 确保Node.js版本16+
2. 检查npm是否正常工作
3. 删除`node_modules`重新安装依赖

### 连接问题
1. 检查防火墙设置
2. 确认端口9912未被占用
3. 查看IDEA日志获取详细错误信息

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

### 开发环境设置
1. 克隆仓库
2. 安装Node.js依赖：`cd src/main/resources/web && npm install`
3. 构建项目：`npm run build`
4. 在IDEA中打开项目并运行
