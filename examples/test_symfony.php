<?php
/**
 * Symfony Dumper测试示例
 * 这个文件展示了如何在Symfony应用中使用dump功能
 */

// 模拟Symfony环境
require_once __DIR__ . '/../vendor/autoload.php';

use Symfony\Component\VarDumper\VarDumper;
use Symfony\Component\VarDumper\Cloner\VarCloner;
use Symfony\Component\VarDumper\Dumper\ContextProvider\CliContextProvider;
use Symfony\Component\VarDumper\Dumper\ContextProvider\SourceContextProvider;
use Symfony\Component\VarDumper\Dumper\HtmlDumper;
use Symfony\Component\VarDumper\Dumper\ServerDumper;

// 配置dump服务器
$cloner = new VarCloner();
$dumper = new ServerDumper('tcp://127.0.0.1:9912', new HtmlDumper(), [
    'cli' => new CliContextProvider(),
    'source' => new SourceContextProvider(),
]);

VarDumper::setHandler(function ($var) use ($cloner, $dumper) {
    $dumper->dump($cloner->cloneVar($var));
});

// 测试数据
echo "Testing Symfony Dumper with IDEA Plugin...\n";

// 1. 简单变量
dump("Hello World!");

// 2. 数组
dump([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'roles' => ['ROLE_USER', 'ROLE_ADMIN'],
    'metadata' => [
        'created_at' => new DateTime(),
        'last_login' => new DateTime('-1 hour'),
        'preferences' => [
            'theme' => 'dark',
            'language' => 'en',
            'notifications' => true
        ]
    ]
]);

// 3. 对象
class User {
    private $id;
    private $username;
    private $email;
    private $roles = [];
    
    public function __construct($id, $username, $email) {
        $this->id = $id;
        $this->username = $username;
        $this->email = $email;
    }
    
    public function addRole($role) {
        $this->roles[] = $role;
    }
    
    public function getRoles() {
        return $this->roles;
    }
}

$user = new User(123, 'johndoe', '<EMAIL>');
$user->addRole('ROLE_USER');
$user->addRole('ROLE_ADMIN');

dump($user);

// 4. 复杂数据结构
$complexData = [
    'request' => [
        'method' => 'POST',
        'uri' => '/api/users',
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer token123',
            'User-Agent' => 'Mozilla/5.0...'
        ],
        'body' => json_decode('{"name":"John","email":"<EMAIL>"}', true)
    ],
    'response' => [
        'status' => 201,
        'headers' => [
            'Content-Type' => 'application/json',
            'Location' => '/api/users/123'
        ],
        'body' => [
            'id' => 123,
            'name' => 'John',
            'email' => '<EMAIL>',
            'created_at' => '2024-01-15T10:30:00Z'
        ]
    ],
    'timing' => [
        'start' => microtime(true),
        'end' => microtime(true) + 0.125,
        'duration' => 0.125
    ]
];

dump($complexData);

// 5. 异常信息
try {
    throw new \Exception('This is a test exception', 500);
} catch (\Exception $e) {
    dump($e);
}

// 6. 资源信息
$resource = fopen(__FILE__, 'r');
dump($resource);
fclose($resource);

echo "All dumps sent to IDEA plugin!\n";
echo "Check the 'Symfony Dumper' tool window in IntelliJ IDEA.\n";
?>
