# Symfony配置示例
# 将此配置添加到你的Symfony项目中

# config/packages/debug.yaml
when@dev:
    debug:
        # 配置dump输出到IDEA插件服务器
        dump_destination: "tcp://127.0.0.1:9912"
        
        # 可选：设置最大dump深度
        max_depth: 6
        
        # 可选：设置最大字符串长度
        max_string_length: 1000

# config/packages/monolog.yaml (可选：同时记录到日志)
when@dev:
    monolog:
        handlers:
            dumper:
                type: stream
                path: "%kernel.logs_dir%/dumper.log"
                level: debug
                channels: ["dumper"]
