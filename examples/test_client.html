<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Symfony Dumper Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 200px;
            font-family: monospace;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .preset-buttons {
            margin-bottom: 15px;
        }
        .preset-buttons button {
            margin-right: 10px;
            margin-bottom: 5px;
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>Symfony Dumper Test Client</h1>
    <p>Use this tool to test the IDEA Symfony Dumper plugin by sending custom data.</p>
    
    <form id="dumperForm">
        <div class="form-group">
            <label for="serverUrl">Server URL:</label>
            <input type="text" id="serverUrl" value="http://localhost:9912" required>
        </div>
        
        <div class="form-group">
            <label for="dataType">Data Type:</label>
            <select id="dataType">
                <option value="json">JSON</option>
                <option value="text">Plain Text</option>
                <option value="html">HTML</option>
                <option value="xml">XML</option>
            </select>
        </div>
        
        <div class="preset-buttons">
            <button type="button" onclick="loadPreset('simple')">Simple Object</button>
            <button type="button" onclick="loadPreset('array')">Array Data</button>
            <button type="button" onclick="loadPreset('user')">User Object</button>
            <button type="button" onclick="loadPreset('error')">Error Data</button>
            <button type="button" onclick="loadPreset('debug')">Debug Info</button>
        </div>
        
        <div class="form-group">
            <label for="dumpData">Data to Dump:</label>
            <textarea id="dumpData" placeholder="Enter your data here..." required>{
  "message": "Hello from web client!",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "user_id": 123,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}</textarea>
        </div>
        
        <button type="submit">Send to Dumper</button>
    </form>
    
    <div id="result"></div>

    <script>
        const presets = {
            simple: {
                type: 'json',
                data: JSON.stringify({
                    message: "Simple test message",
                    timestamp: new Date().toISOString(),
                    value: 42
                }, null, 2)
            },
            array: {
                type: 'json',
                data: JSON.stringify({
                    items: [
                        { id: 1, name: "Item 1", active: true },
                        { id: 2, name: "Item 2", active: false },
                        { id: 3, name: "Item 3", active: true }
                    ],
                    total: 3,
                    page: 1
                }, null, 2)
            },
            user: {
                type: 'json',
                data: JSON.stringify({
                    id: 123,
                    username: "johndoe",
                    email: "<EMAIL>",
                    profile: {
                        firstName: "John",
                        lastName: "Doe",
                        avatar: "https://example.com/avatar.jpg",
                        preferences: {
                            theme: "dark",
                            language: "en",
                            notifications: true
                        }
                    },
                    roles: ["ROLE_USER", "ROLE_ADMIN"],
                    lastLogin: new Date().toISOString(),
                    metadata: {
                        createdAt: "2023-01-15T10:30:00Z",
                        updatedAt: new Date().toISOString(),
                        loginCount: 42
                    }
                }, null, 2)
            },
            error: {
                type: 'json',
                data: JSON.stringify({
                    error: {
                        type: "ValidationException",
                        message: "Invalid input data",
                        code: 422,
                        details: {
                            field: "email",
                            value: "invalid-email",
                            constraint: "email format"
                        },
                        trace: [
                            "at validateEmail (validator.js:15:3)",
                            "at processUser (user.js:42:7)",
                            "at handleRequest (controller.js:28:5)"
                        ]
                    },
                    request: {
                        method: "POST",
                        url: "/api/users",
                        body: { email: "invalid-email" }
                    },
                    timestamp: new Date().toISOString()
                }, null, 2)
            },
            debug: {
                type: 'json',
                data: JSON.stringify({
                    debug: {
                        memory: {
                            used: "15.2 MB",
                            peak: "18.7 MB",
                            limit: "128 MB"
                        },
                        timing: {
                            start: Date.now() - 1000,
                            current: Date.now(),
                            duration: "1.234s"
                        },
                        database: {
                            queries: 15,
                            totalTime: "45.6ms",
                            slowQueries: 2
                        },
                        cache: {
                            hits: 23,
                            misses: 7,
                            ratio: "76.7%"
                        }
                    },
                    environment: {
                        php: "8.2.0",
                        symfony: "6.4.0",
                        environment: "dev"
                    }
                }, null, 2)
            }
        };

        function loadPreset(presetName) {
            const preset = presets[presetName];
            if (preset) {
                document.getElementById('dataType').value = preset.type;
                document.getElementById('dumpData').value = preset.data;
            }
        }

        document.getElementById('dumperForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const serverUrl = document.getElementById('serverUrl').value;
            const dataType = document.getElementById('dataType').value;
            const dumpData = document.getElementById('dumpData').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch(serverUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': getContentType(dataType),
                    },
                    body: dumpData
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>Success!</strong> Data sent to dumper server.
                            <br>Status: ${response.status}
                            <br>Time: ${new Date().toLocaleTimeString()}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>Error!</strong> Failed to send data.
                            <br>Status: ${response.status} ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>Error!</strong> ${error.message}
                        <br>Make sure the dumper server is running on the specified URL.
                    </div>
                `;
            }
        });

        function getContentType(dataType) {
            switch (dataType) {
                case 'json': return 'application/json';
                case 'html': return 'text/html';
                case 'xml': return 'application/xml';
                default: return 'text/plain';
            }
        }
    </script>
</body>
</html>
