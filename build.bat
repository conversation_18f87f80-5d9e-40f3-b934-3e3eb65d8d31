@echo off
echo Building Symfony Dumper IDEA Plugin...

REM Check if JAVA_HOME is set
if "%JAVA_HOME%"=="" (
    echo ERROR: JAVA_HOME is not set
    echo Please set JAVA_HOME to point to your Java installation
    echo Example: set JAVA_HOME=C:\Program Files\Java\jdk-21
    pause
    exit /b 1
)

REM Check if Java is accessible
"%JAVA_HOME%\bin\java" -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java not found at %JAVA_HOME%\bin\java
    echo Please check your JAVA_HOME setting
    pause
    exit /b 1
)

echo Using Java at: %JAVA_HOME%
echo.

REM Run Gradle build
call gradlew.bat buildPlugin

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    pause
    exit /b 1
)

echo.
echo BUILD SUCCESSFUL!
echo Plugin file created at: build\distributions\thinkphp-1.0-SNAPSHOT.zip
echo.
echo To install the plugin:
echo 1. Open IntelliJ IDEA
echo 2. Go to File ^> Settings ^> Plugins
echo 3. Click the gear icon and select "Install Plugin from Disk..."
echo 4. Select the generated ZIP file
echo 5. Restart IDEA
echo.
pause
