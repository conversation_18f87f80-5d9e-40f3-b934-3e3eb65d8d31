@echo off
echo Building Symfony Dumper IDEA Plugin with Web UI...

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version

REM Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)

echo npm version:
npm --version

REM Navigate to web directory
cd src\main\resources\web

REM Install dependencies
echo Installing Node.js dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install Node.js dependencies
    pause
    exit /b 1
)

REM Build TypeScript and bundle
echo Building TypeScript and bundling...
call npm run build
if errorlevel 1 (
    echo ERROR: Failed to build TypeScript
    pause
    exit /b 1
)

REM Go back to root directory
cd ..\..\..\..

REM Check if JAVA_HOME is set
if "%JAVA_HOME%"=="" (
    echo ERROR: JAVA_HOME is not set
    echo Please set JAVA_HOME to point to your Java installation
    echo Example: set JAVA_HOME=C:\Program Files\Java\jdk-21
    pause
    exit /b 1
)

REM Build the plugin
echo Building IDEA plugin...
call gradlew.bat buildPlugin

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    pause
    exit /b 1
)

echo.
echo BUILD SUCCESSFUL!
echo Plugin file created at: build\distributions\thinkphp-1.0-SNAPSHOT.zip
echo.
echo The plugin includes both:
echo - Web UI (TypeScript/HTML/CSS) - Modern interface with real-time updates
echo - Classic UI (Kotlin/Swing) - Traditional Swing interface
echo.
echo To install the plugin:
echo 1. Open IntelliJ IDEA
echo 2. Go to File ^> Settings ^> Plugins
echo 3. Click the gear icon and select "Install Plugin from Disk..."
echo 4. Select the generated ZIP file
echo 5. Restart IDEA
echo 6. Open the "Symfony Dumper" tool window at the bottom
echo 7. Choose between "Web UI" and "Classic UI" tabs
echo.
pause
